# Real-Time Chat Application

A modern real-time chat application built with React, Node.js, Express, Socket.io, and MongoDB.

## Features

### Core Features
- ✅ User authentication (register/login)
- ✅ Real-time messaging with WebSocket technology
- ✅ Create and join chat rooms
- ✅ Private messaging between users
- ✅ User presence indicators (online/offline/away)

### Optional Features
- ✅ Chat history and message persistence
- ✅ Browser notifications for new messages
- ✅ Typing indicators
- ✅ File sharing capabilities
- ✅ Message delivery status
- ✅ Responsive design

## Tech Stack

### Backend
- Node.js
- Express.js
- Socket.io (WebSocket)
- MongoDB with Mongoose
- JWT for authentication
- Bcrypt for password hashing
- Multer for file uploads

### Frontend
- React 18
- Material-UI (MUI)
- Socket.io Client
- React Router
- Axios for HTTP requests
- React Hook Form
- Vite for build tooling

## Prerequisites

- Node.js (v16 or higher)
- MongoDB (local or cloud instance)
- npm or yarn

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd chat_application
```

2. Install dependencies for all packages:
```bash
npm run install:all
```

3. Set up environment variables:
```bash
cd server
cp .env.example .env
# Edit .env with your configuration
```

4. Start MongoDB (if running locally)

5. Run the application in development mode:
```bash
npm run dev
```

This will start:
- Backend server on http://localhost:5000
- Frontend development server on http://localhost:3000

## Project Structure

```
chat_application/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services
│   │   └── utils/          # Utility functions
│   └── package.json
├── server/                 # Node.js backend
│   ├── config/             # Database and other configs
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Custom middleware
│   ├── models/             # MongoDB models
│   ├── routes/             # API routes
│   ├── socket/             # Socket.io handlers
│   └── package.json
└── package.json           # Root package.json
```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user

### Chat
- `GET /api/chat/rooms` - Get all chat rooms
- `POST /api/chat/rooms` - Create new chat room
- `GET /api/chat/rooms/:id/messages` - Get room messages
- `POST /api/chat/upload` - Upload file

### Users
- `GET /api/users` - Get all users
- `PUT /api/users/status` - Update user status

## Socket Events

### Client to Server
- `join_room` - Join a chat room
- `leave_room` - Leave a chat room
- `send_message` - Send a message
- `typing` - User is typing
- `stop_typing` - User stopped typing

### Server to Client
- `message` - New message received
- `user_joined` - User joined room
- `user_left` - User left room
- `typing` - Someone is typing
- `stop_typing` - Someone stopped typing
- `user_status_change` - User status changed

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License
