const express = require('express');
const router = express.Router();

const chatController = require('../controllers/chatController');
const messageController = require('../controllers/messageController');
const { authenticateToken } = require('../middleware/auth');
const { validate } = require('../middleware/validation');

// Room routes
router.get('/rooms', authenticateToken, chatController.getRooms);
router.get('/rooms/my', authenticateToken, chatController.getMyRooms);
router.post('/rooms', authenticateToken, validate('createRoom'), chatController.createRoom);
router.get('/rooms/:roomId', authenticateToken, chatController.getRoomDetails);
router.post('/rooms/:roomId/join', authenticateToken, chatController.joinRoom);
router.post('/rooms/:roomId/leave', authenticateToken, chatController.leaveRoom);

// Message routes
router.post('/messages', authenticateToken, validate('sendMessage'), messageController.sendMessage);
router.get('/rooms/:roomId/messages', authenticateToken, messageController.getRoomMessages);
router.get('/conversations', authenticateToken, messageController.getConversations);
router.get('/conversations/:userId', authenticateToken, messageController.getConversation);
router.put('/messages/:messageId/read', authenticateToken, messageController.markMessageAsRead);

module.exports = router;
