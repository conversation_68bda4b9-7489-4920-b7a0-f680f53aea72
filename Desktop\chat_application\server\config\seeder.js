const mongoose = require('mongoose');
const { User, Room } = require('../models');
require('dotenv').config();

const seedData = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/chat_app');
    console.log('Connected to MongoDB for seeding...');

    // Clear existing data (optional - uncomment if needed)
    // await User.deleteMany({});
    // await Room.deleteMany({});
    // console.log('Cleared existing data');

    // Create default admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      const admin = new User({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        status: 'online'
      });
      await admin.save();
      console.log('Admin user created');

      // Create default public rooms
      const defaultRooms = [
        {
          name: 'General',
          description: 'General discussion for everyone',
          type: 'public',
          creator: admin._id
        },
        {
          name: 'Random',
          description: 'Random conversations and fun topics',
          type: 'public',
          creator: admin._id
        },
        {
          name: 'Tech Talk',
          description: 'Discuss technology, programming, and innovation',
          type: 'public',
          creator: admin._id
        }
      ];

      for (const roomData of defaultRooms) {
        const existingRoom = await Room.findOne({ name: roomData.name });
        if (!existingRoom) {
          const room = new Room(roomData);
          // Add admin as first member
          room.members.push({
            user: admin._id,
            role: 'admin',
            joinedAt: new Date()
          });
          await room.save();
          console.log(`Created room: ${room.name}`);
        }
      }
    } else {
      console.log('Admin user already exists');
    }

    console.log('Database seeding completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedData();
}

module.exports = seedData;
