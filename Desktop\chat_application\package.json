{"name": "chat-application", "version": "1.0.0", "description": "Real-time chat application with WebSocket technology", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "server:start": "cd server && npm start", "client:build": "cd client && npm run build", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test"}, "keywords": ["chat", "websocket", "real-time", "messaging"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}