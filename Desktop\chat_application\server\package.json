{"name": "chat-server", "version": "1.0.0", "description": "Backend server for real-time chat application", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}